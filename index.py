import cv2
import torch
import numpy as np
import time
from collections import defaultdict
import math

# Ganti 'http://source-url.com/stream' dengan URL sumber video
stream_url = 'http://stream.cctv.malangkota.go.id/WebRTCApp/streams/697348239578014654770613.m3u8'

# Memuat model YOLOv5 dengan force_reload untuk mengatasi cache yang rusak
try:
    print("Loading YOLOv5 model...")
    model = torch.hub.load('ultralytics/yolov5', 'yolov5m')  # Model medium YOLOv5 (yolov5m)
    print("YOLOv5m model loaded successfully!")
except Exception as e:
    print(f"Error loading yolov5m: {e}")
    print("Trying yolov5s as fallback...")
    try:
        model = torch.hub.load('ultralytics/yolov5', 'yolov5s')  # Fallback ke model kecil
        print("YOLOv5s model loaded successfully!")
    except Exception as e2:
        print(f"Error loading yolov5s: {e2}")
        print("Trying local model files...")
        # Coba gunakan file lokal jika ada
        if torch.cuda.is_available():
            device = 'cuda'
        else:
            device = 'cpu'

        # Coba load model lokal
        try:
            model = torch.hub.load('ultralytics/yolov5', 'custom', path='yolov5s.pt')
            print("Local YOLOv5s model loaded successfully!")
        except:
            print("Loading basic YOLOv5s model...")
            model = torch.hub.load('ultralytics/yolov5', 'yolov5s', trust_repo=True)

# Konfigurasi model untuk deteksi yang seimbang
model.conf = 0.15  # Confidence threshold sangat rendah untuk menangkap motor yang sulit terdeteksi
model.iou = 0.2    # IoU threshold lebih rendah untuk mendeteksi objek yang berdekatan
model.max_det = 1000  # Maksimum deteksi per gambar

# Class IDs untuk semua jenis kendaraan di COCO dataset
VEHICLE_CLASSES = [
    2,   # car
    3,   # motorcycle
    5,   # bus
    7,   # truck
    1,   # bicycle (sepeda juga kendaraan)
]

# Fungsi untuk validasi ukuran objek berdasarkan jenis kendaraan
def validate_vehicle_size(x1, y1, x2, y2, class_id, frame_width, frame_height):
    """Validasi ukuran objek untuk mengurangi false positive"""
    width = x2 - x1
    height = y2 - y1
    area = width * height

    # Persentase area relatif terhadap frame
    relative_area = area / (frame_width * frame_height)

    # Rasio aspek (width/height)
    aspect_ratio = width / height if height > 0 else 0

    # Validasi berdasarkan jenis kendaraan (parameter lebih fleksibel)
    if class_id == 2:  # car
        # Mobil: area 0.0005-0.4, aspect ratio 0.8-4.0 (lebih fleksibel)
        return 0.0005 <= relative_area <= 0.4 and 0.8 <= aspect_ratio <= 4.0
    elif class_id == 3:  # motorcycle
        # Motor: area 0.00005-0.3, aspect ratio 0.2-5.0 (sangat fleksibel untuk motor berjajar dan kecil)
        return 0.00005 <= relative_area <= 0.3 and 0.2 <= aspect_ratio <= 5.0
    elif class_id == 5:  # bus
        # Bus: area 0.005-0.6, aspect ratio 1.5-5.0 (lebih fleksibel)
        return 0.005 <= relative_area <= 0.6 and 1.5 <= aspect_ratio <= 5.0
    elif class_id == 7:  # truck
        # Truk: area 0.003-0.5, aspect ratio 1.0-4.0 (lebih fleksibel)
        return 0.003 <= relative_area <= 0.5 and 1.0 <= aspect_ratio <= 4.0
    elif class_id == 1:  # bicycle
        # Sepeda: area 0.0001-0.08, aspect ratio 0.5-2.5 (lebih fleksibel)
        return 0.0001 <= relative_area <= 0.08 and 0.5 <= aspect_ratio <= 2.5

    return True  # Default untuk kelas lain

# Fungsi untuk validasi tambahan khusus motor dengan confidence rendah
def validate_low_confidence_motorcycle(x1, y1, x2, y2, conf, frame_width, frame_height):
    """Validasi ketat untuk motor dengan confidence rendah untuk mengurangi false positive"""
    width = x2 - x1
    height = y2 - y1
    area = width * height
    relative_area = area / (frame_width * frame_height)
    aspect_ratio = width / height if height > 0 else 0
    
    # Untuk motor dengan confidence sangat rendah, validasi lebih ketat
    if conf < 0.15:
        # Harus memenuhi kriteria ukuran yang lebih ketat
        size_valid = (0.0001 <= relative_area <= 0.1 and 
                     0.4 <= aspect_ratio <= 3.5)
        
        # Harus memiliki ukuran minimum yang masuk akal untuk motor
        min_size_valid = width >= 15 and height >= 15
        
        # Tidak boleh terlalu besar (kemungkinan false positive)
        max_size_valid = relative_area <= 0.05
        
        return size_valid and min_size_valid and max_size_valid
    
    # Untuk confidence sedang, validasi normal
    return True

# Fungsi untuk mendeteksi dan menolak false positive umum
def is_likely_false_positive(x1, y1, x2, y2, conf, class_id, frame_width, frame_height):
    """Deteksi objek yang kemungkinan false positive"""
    width = x2 - x1
    height = y2 - y1
    area = width * height
    relative_area = area / (frame_width * frame_height)
    aspect_ratio = width / height if height > 0 else 0
    center_y = (y1 + y2) / 2
    
    # Khusus untuk motor dengan confidence rendah
    if class_id == 3 and conf < 0.2:
        false_positive_score = 0
        
        # 1. Objek terlalu kecil untuk menjadi motor
        if relative_area < 0.00005:  # Sangat kecil
            false_positive_score += 2
            
        # 2. Aspect ratio yang sangat tidak wajar untuk motor
        if aspect_ratio < 0.15 or aspect_ratio > 6.0:
            false_positive_score += 2
            
        # 3. Objek di area yang sangat tidak mungkin (pojok atas frame)
        if center_y < frame_height * 0.05:  # 5% teratas
            false_positive_score += 1
            
        # 4. Objek terlalu tipis (kemungkinan garis atau bayangan)
        if width < 10 or height < 10:
            false_positive_score += 2
            
        # 5. Objek dengan ukuran yang sangat tidak proporsional
        if width > height * 8 or height > width * 8:  # Sangat tidak proporsional
            false_positive_score += 1
            
        # 6. Area terlalu besar untuk confidence yang rendah
        if relative_area > 0.02 and conf < 0.1:  # Besar tapi confidence rendah
            false_positive_score += 1
            
        # Jika skor false positive tinggi, kemungkinan bukan motor
        return false_positive_score >= 3
    
    return False

# ===== SISTEM TRACKING KENDARAAN UNIK =====

class VehicleTracker:
    """Sistem tracking untuk menghitung kendaraan unik"""
    
    def __init__(self):
        self.tracked_vehicles = {}  # ID -> vehicle info
        self.next_id = 1
        self.max_disappeared = 30  # Frame maksimum sebelum dianggap hilang
        self.max_distance = 100    # Jarak maksimum untuk menganggap objek sama
        self.min_frames_to_count = 5  # Minimum frame untuk dihitung sebagai kendaraan valid
        
        # Statistik
        self.unique_counts = defaultdict(int)  # Hitung per jenis kendaraan
        self.total_unique = 0
        
    def calculate_distance(self, center1, center2):
        """Hitung jarak Euclidean antara dua titik"""
        return math.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)
    
    def calculate_iou(self, box1, box2):
        """Hitung Intersection over Union (IoU) antara dua bounding box"""
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        # Hitung area intersection
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
            
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0

    def update(self, detections, frame_count):
        """Update tracking dengan deteksi baru"""
        current_detections = []

        # Konversi deteksi ke format yang mudah diproses
        for detection in detections:
            x1, y1, x2, y2, conf, class_id = detection
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2

            current_detections.append({
                'bbox': (x1, y1, x2, y2),
                'center': (center_x, center_y),
                'class_id': int(class_id),
                'confidence': conf,
                'matched': False
            })

        # Update existing vehicles
        for vehicle_id in list(self.tracked_vehicles.keys()):
            vehicle = self.tracked_vehicles[vehicle_id]
            vehicle['disappeared'] += 1
            vehicle['matched'] = False

        # Match deteksi dengan kendaraan yang sudah ada
        for detection in current_detections:
            best_match_id = None
            best_iou = 0

            for vehicle_id, vehicle in self.tracked_vehicles.items():
                if vehicle['class_id'] != detection['class_id']:
                    continue

                # Hitung jarak center
                distance = self.calculate_distance(detection['center'], vehicle['center'])

                # Hitung IoU
                iou = self.calculate_iou(detection['bbox'], vehicle['bbox'])

                # Kriteria matching: jarak dekat DAN IoU tinggi
                if distance < self.max_distance and iou > 0.1:
                    # Prioritaskan IoU tinggi, tapi pertimbangkan jarak juga
                    score = iou * 0.7 + (1 - distance / self.max_distance) * 0.3

                    if score > best_iou:
                        best_match_id = vehicle_id
                        best_iou = score

            # Jika ada match yang bagus
            if best_match_id is not None and best_iou > 0.3:
                vehicle = self.tracked_vehicles[best_match_id]

                # Update informasi kendaraan
                vehicle['bbox'] = detection['bbox']
                vehicle['center'] = detection['center']
                vehicle['confidence'] = detection['confidence']
                vehicle['disappeared'] = 0
                vehicle['frames_seen'] += 1
                vehicle['last_seen'] = frame_count
                vehicle['matched'] = True
                detection['matched'] = True

                # Jika sudah cukup lama terlihat dan belum dihitung, hitung sebagai unik
                if (vehicle['frames_seen'] >= self.min_frames_to_count and
                    not vehicle['counted_as_unique']):
                    vehicle['counted_as_unique'] = True
                    self.unique_counts[vehicle['class_id']] += 1
                    self.total_unique += 1

        # Tambah kendaraan baru untuk deteksi yang tidak match
        for detection in current_detections:
            if not detection['matched']:
                self.tracked_vehicles[self.next_id] = {
                    'bbox': detection['bbox'],
                    'center': detection['center'],
                    'class_id': detection['class_id'],
                    'confidence': detection['confidence'],
                    'first_seen': frame_count,
                    'last_seen': frame_count,
                    'frames_seen': 1,
                    'disappeared': 0,
                    'counted_as_unique': False,
                    'matched': True
                }
                self.next_id += 1

        # Hapus kendaraan yang sudah lama hilang
        to_remove = []
        for vehicle_id, vehicle in self.tracked_vehicles.items():
            if vehicle['disappeared'] > self.max_disappeared:
                to_remove.append(vehicle_id)

        for vehicle_id in to_remove:
            del self.tracked_vehicles[vehicle_id]

    def get_active_vehicles(self):
        """Dapatkan kendaraan yang sedang aktif (terlihat)"""
        active = []
        for vehicle_id, vehicle in self.tracked_vehicles.items():
            if vehicle['disappeared'] == 0:  # Baru saja terlihat
                active.append((vehicle_id, vehicle))
        return active

    def get_statistics(self):
        """Dapatkan statistik tracking"""
        vehicle_names = {1: "Sepeda", 2: "Mobil", 3: "Motor", 5: "Bus", 7: "Truk"}

        stats = {
            'total_unique': self.total_unique,
            'by_type': {},
            'currently_tracked': len([v for v in self.tracked_vehicles.values() if v['disappeared'] < 5])
        }

        for class_id, count in self.unique_counts.items():
            vehicle_name = vehicle_names.get(class_id, f"Kendaraan_{class_id}")
            stats['by_type'][vehicle_name] = count

        return stats

    def reset_counts(self):
        """Reset semua hitungan"""
        self.unique_counts.clear()
        self.total_unique = 0
        self.tracked_vehicles.clear()
        self.next_id = 1

# Fungsi untuk validasi posisi objek (ROI - Region of Interest)
def is_in_road_area(x1, y1, x2, y2, frame_width, frame_height):
    """Validasi apakah objek berada di area jalan (bukan trotoar)"""
    center_x = (x1 + x2) / 2
    center_y = (y1 + y2) / 2

    # Definisi area jalan (sangat luas untuk motor di pinggir)
    # Area yang sangat fleksibel untuk motor yang parkir/berhenti di pinggir
    road_left = frame_width * 0.05   # 5% dari kiri (sangat sempit untuk motor pinggir)
    road_right = frame_width * 0.95  # 5% dari kanan (sangat sempit untuk motor pinggir)
    road_top = frame_height * 0.1    # 10% dari atas (lebih rendah lagi)

    # Objek harus berada di area jalan
    return road_left <= center_x <= road_right and center_y >= road_top

# Membuka stream
cap = cv2.VideoCapture(stream_url)
cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Buffer lebih kecil untuk real-time
cap.set(cv2.CAP_PROP_FPS, 30)  # Set FPS

if not cap.isOpened():
    print("Gagal membuka stream!")
    exit()

# Mengurangi frame skipping untuk deteksi yang lebih baik
frame_skip = 1  # Proses setiap frame (atau ubah ke 2 jika performa lambat)
frame_count = 0
vehicle_count = 0  # Counter untuk kendaraan yang terdeteksi (per frame)
strict_filtering = True  # Toggle untuk filtering false positive yang ketat

# Inisialisasi vehicle tracker untuk menghitung kendaraan unik
vehicle_tracker = VehicleTracker()

print("Program dimulai. Tekan 'q' untuk keluar.")
print("Kontrol: 'm'=mode sensitif | 'f'=toggle filter | 'u'=reset unik | 'r'=reset frame")
print("=" * 60)

try:
    while True:
        ret, frame = cap.read()

        if not ret:
            print("Gagal membaca frame! Mencoba reconnect...")
            # Coba reconnect ke stream
            cap.release()
            time.sleep(2)  # Tunggu 2 detik sebelum reconnect
            cap = cv2.VideoCapture(stream_url)
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            cap.set(cv2.CAP_PROP_FPS, 30)
            continue  # Lanjut ke iterasi berikutnya, jangan break

        try:
            # Simpan frame original untuk display
            original_frame = frame.copy()

            # Resize dengan resolusi yang lebih tinggi untuk deteksi yang lebih baik
            frame = cv2.resize(frame, (1280, 720))  # Resolusi lebih tinggi
        except Exception as e:
            print(f"Error processing frame: {e}")
            continue  # Skip frame ini dan lanjut ke frame berikutnya

        frame_count += 1
        if frame_count % frame_skip != 0:
            continue  # Lewati frame

        try:
            # Preprocessing untuk meningkatkan deteksi motor yang sulit terlihat
            # Peningkatan kontras dan brightness untuk motor yang gelap/tertutup
            enhanced_frame = cv2.convertScaleAbs(frame, alpha=1.15, beta=15)  # Peningkatan kontras dan brightness

            # Gabungkan frame original dan enhanced untuk deteksi yang lebih baik
            # Gunakan enhanced frame untuk deteksi
            detection_frame = enhanced_frame

            # Deteksi objek menggunakan YOLOv5 dengan frame yang telah dienhance
            results = model(detection_frame)  # Hasil deteksi dari enhanced frame

            # Ambil hasil deteksi
            detections = results.xyxy[0].cpu().numpy()  # Bounding boxes (x1, y1, x2, y2, conf, class)
        except Exception as e:
            print(f"Error during detection: {e}")
            # Jika ada error deteksi, gunakan frame kosong untuk display
            display_frame = cv2.resize(original_frame, (1280, 720))
            cv2.putText(display_frame, f"Detection Error: {str(e)[:50]}", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            cv2.imshow('Live Streaming - Deteksi Kendaraan (Enhanced)', display_frame)

            # Tetap cek keyboard input
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            continue

        # Reset frame untuk display (gunakan original frame yang di-resize)
        display_frame = cv2.resize(original_frame, (1280, 720))

        # Counter untuk frame ini
        current_frame_vehicles = 0
        debug_info = []  # Untuk menyimpan info debugging

        # Kumpulkan deteksi yang valid untuk tracking
        valid_detections_for_tracking = []

        # Loop untuk setiap deteksi
        for detection in detections:
            x1, y1, x2, y2, conf, class_id = detection
            class_id = int(class_id)

            # Filter deteksi kendaraan dengan validasi yang lebih fleksibel
            # Cek confidence threshold yang disesuaikan (sangat rendah untuk motor)
            if class_id == 3:  # Motor
                confidence_ok = conf > 0.05   # Threshold sangat rendah untuk motor (diperluas)
            else:
                confidence_ok = conf > 0.3   # Threshold normal untuk kendaraan lain

            # Cek apakah kelas kendaraan
            is_vehicle = class_id in VEHICLE_CLASSES

            # Validasi ukuran (opsional untuk kendaraan kecil/jauh)
            size_ok = validate_vehicle_size(x1, y1, x2, y2, class_id, 1280, 720)

            # Validasi ROI (opsional untuk kendaraan di pinggir)
            roi_ok = is_in_road_area(x1, y1, x2, y2, 1280, 720)

            # Cek apakah kemungkinan false positive (hanya jika strict filtering aktif)
            is_false_positive = strict_filtering and is_likely_false_positive(x1, y1, x2, y2, conf, class_id, 1280, 720)

            # Logika deteksi yang lebih fleksibel, khusus untuk motor:
            # - Confidence tinggi (>0.5): langsung terima tanpa validasi lain
            # - Confidence sedang (0.3-0.5): perlu validasi ukuran ATAU ROI
            # - Confidence rendah (<0.3): tolak, kecuali motor dengan validasi khusus
            if confidence_ok and is_vehicle and not is_false_positive:  # Tambahkan filter false positive
                if conf > 0.5:  # Confidence tinggi, langsung terima
                    should_detect = True
                elif conf > 0.4:  # Confidence sedang-tinggi, perlu salah satu validasi
                    should_detect = size_ok or roi_ok
                elif class_id == 3:  # Motor dengan confidence sedang-rendah, validasi khusus
                    if conf < 0.15:  # Motor confidence sangat rendah, validasi ketat
                        low_conf_valid = validate_low_confidence_motorcycle(x1, y1, x2, y2, conf, 1280, 720)
                        should_detect = low_conf_valid and (size_ok or roi_ok)
                    else:  # Motor confidence sedang-rendah, lebih toleran
                        should_detect = size_ok or roi_ok
                else:  # Kendaraan lain dengan confidence sedang-rendah, perlu kedua validasi
                    should_detect = size_ok and roi_ok

            else:
                should_detect = False

            # Debug info untuk semua deteksi kendaraan (terdeteksi atau tidak)
            if is_vehicle:
                width = x2 - x1
                height = y2 - y1
                area_percent = (width * height) / (1280 * 720) * 100
                aspect_ratio = width / height if height > 0 else 0
                center_x = (x1 + x2) / 2
                center_y = (y1 + y2) / 2

                vehicle_names = {2: "Mobil", 3: "Motor", 5: "Bus", 7: "Truk", 1: "Sepeda"}
                vehicle_name = vehicle_names.get(class_id, "Kendaraan")

                debug_info.append({
                    'type': vehicle_name,
                    'conf': conf,
                    'area': area_percent,
                    'ratio': aspect_ratio,
                    'pos': (center_x, center_y),
                    'size_ok': size_ok,
                    'roi_ok': roi_ok,
                    'false_positive': is_false_positive,
                    'detected': should_detect
                })

            if should_detect:
                current_frame_vehicles += 1

                # Tambahkan ke list untuk tracking
                valid_detections_for_tracking.append([x1, y1, x2, y2, conf, class_id])

                # Tentukan warna berdasarkan jenis kendaraan
                if class_id == 2:  # car
                    color = (0, 255, 0)  # Hijau
                    vehicle_type = "Mobil"
                elif class_id == 3:  # motorcycle
                    # Warna berbeda untuk motor berdasarkan confidence dan kondisi
                    if conf > 0.4:
                        color = (0, 0, 255)  # Merah untuk motor confidence tinggi
                        vehicle_type = "Motor"
                    elif conf > 0.3:
                        color = (0, 100, 255)  # Orange untuk motor berjajar
                        vehicle_type = "Motor*"
                    elif conf > 0.2:
                        color = (255, 0, 255)  # Magenta untuk motor berdampingan
                        vehicle_type = "Motor="  # Tanda = untuk motor berdampingan
                    elif conf > 0.1:
                        color = (0, 255, 255)  # Kuning untuk motor tertutup
                        vehicle_type = "Motor#"  # Tanda # untuk motor tertutup
                    else:
                        color = (255, 255, 0)  # Cyan untuk motor di pinggir
                        vehicle_type = "Motor~"  # Tanda ~ untuk motor pinggir
                elif class_id == 5:  # bus
                    color = (255, 0, 0)  # Biru
                    vehicle_type = "Bus"
                elif class_id == 7:  # truck
                    color = (0, 255, 255)  # Kuning
                    vehicle_type = "Truk"
                elif class_id == 1:  # bicycle
                    color = (255, 0, 255)  # Magenta
                    vehicle_type = "Sepeda"
                else:
                    color = (128, 128, 128)  # Abu-abu
                    vehicle_type = "Kendaraan"

                # Gambar bounding box dengan ketebalan yang lebih tebal
                cv2.rectangle(display_frame, (int(x1), int(y1)), (int(x2), int(y2)), color, 3)

                # Label dengan informasi lebih detail termasuk ukuran untuk debugging
                width = x2 - x1
                height = y2 - y1
                area_percent = (width * height) / (1280 * 720) * 100
                label = f"{vehicle_type}: {conf:.2f} ({area_percent:.1f}%)"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]

                # Background untuk label
                cv2.rectangle(display_frame, (int(x1), int(y1)-label_size[1]-10),
                             (int(x1)+label_size[0], int(y1)), color, -1)

                # Text label
                cv2.putText(display_frame, label, (int(x1), int(y1)-5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        # Update total vehicle count (per frame)
        if current_frame_vehicles > 0:
            vehicle_count += current_frame_vehicles

        # Update vehicle tracking untuk menghitung kendaraan unik
        vehicle_tracker.update(valid_detections_for_tracking, frame_count)

        # Dapatkan statistik tracking
        tracking_stats = vehicle_tracker.get_statistics()
        active_vehicles = vehicle_tracker.get_active_vehicles()

        # Tambahkan ID tracking pada kendaraan yang terdeteksi
        for vehicle_id, vehicle_info in active_vehicles:
            tracked_bbox = vehicle_info['bbox']
            tracked_center = vehicle_info['center']

            # Gambar ID tracking
            id_text = f"ID:{vehicle_id}"
            if vehicle_info['counted_as_unique']:
                id_text += " ✓"  # Tanda sudah dihitung sebagai unik

            cv2.putText(display_frame, id_text, (int(tracked_bbox[0]), int(tracked_bbox[3])+20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

            # Gambar lingkaran kecil di center untuk tracking
            cv2.circle(display_frame, (int(tracked_center[0]), int(tracked_center[1])), 3, (0, 255, 0), -1)

        # Gambar ROI (Region of Interest) untuk debugging
        road_left = int(1280 * 0.05)
        road_right = int(1280 * 0.95)
        road_top = int(720 * 0.1)
        cv2.line(display_frame, (road_left, 0), (road_left, 720), (255, 255, 0), 2)  # Garis kiri
        cv2.line(display_frame, (road_right, 0), (road_right, 720), (255, 255, 0), 2)  # Garis kanan
        cv2.line(display_frame, (0, road_top), (1280, road_top), (255, 255, 0), 2)  # Garis atas

        # Tambahkan informasi statistik di frame
        info_text = f"Frame: {current_frame_vehicles} | Total Frame: {vehicle_count} | UNIK: {tracking_stats['total_unique']}"
        cv2.putText(display_frame, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        cv2.putText(display_frame, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 1)

        # Tambahkan informasi tracking detail
        tracking_text = f"Sedang ditrack: {tracking_stats['currently_tracked']}"
        cv2.putText(display_frame, tracking_text, (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(display_frame, tracking_text, (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

        # Tambahkan informasi frame dan confidence threshold
        filter_status = "ON" if strict_filtering else "OFF"
        frame_info = f"Frame: {frame_count} | Conf: {model.conf:.2f} | FP Filter: {filter_status}"
        cv2.putText(display_frame, frame_info, (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
        cv2.putText(display_frame, frame_info, (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)

        # Tambahkan informasi kontrol
        control_info = "'m'=sensitif | 'f'=filter | 'u'=reset unik | 'r'=reset frame | 'q'=KELUAR"
        cv2.putText(display_frame, control_info, (10, 110), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 2)
        cv2.putText(display_frame, control_info, (10, 110), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 0), 1)

        # Tambahkan status koneksi
        connection_status = f"Stream: CONNECTED | Frame: {frame_count}"
        cv2.putText(display_frame, connection_status, (10, 680), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

        # Tambahkan statistik kendaraan unik per jenis
        stats_y = 150
        if tracking_stats['by_type']:
            unique_info = "KENDARAAN UNIK: "
            for vehicle_type, count in tracking_stats['by_type'].items():
                unique_info += f"{vehicle_type}:{count} "
            cv2.putText(display_frame, unique_info, (10, stats_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            stats_y += 25

        try:
            # Tampilkan frame dengan deteksi kendaraan
            cv2.imshow('Live Streaming - Deteksi Kendaraan (Enhanced)', display_frame)

            # Tekan 'q' untuk keluar, 'r' untuk reset counter, '+'/'-' untuk adjust confidence
            key = cv2.waitKey(1) & 0xFF

            # Handle keyboard input
            if key == ord('q'):
                print("Program dihentikan oleh user (tekan 'q')")
                break
            elif key == ord('r'):
                vehicle_count = 0
                print("Counter kendaraan per frame direset!")
            elif key == ord('u'):
                vehicle_tracker.reset_counts()
                print("Counter kendaraan UNIK direset!")
                print(f"Total kendaraan unik sebelumnya: {tracking_stats['total_unique']}")
            elif key == ord('+') or key == ord('='):
                model.conf = min(0.9, model.conf + 0.05)
                print(f"Confidence threshold dinaikkan ke: {model.conf:.2f}")
            elif key == ord('-'):
                model.conf = max(0.05, model.conf - 0.05)  # Minimum threshold lebih rendah
                print(f"Confidence threshold diturunkan ke: {model.conf:.2f}")
            elif key == ord('m'):
                # Toggle mode deteksi motor - turunkan threshold khusus untuk motor
                if model.conf > 0.1:
                    model.conf = 0.05
                    print("Mode deteksi motor ULTRA SENSITIF aktif! (conf=0.05)")
                else:
                    model.conf = 0.25
                    print("Mode deteksi normal aktif! (conf=0.25)")
            elif key == ord('f'):
                # Toggle strict false positive filtering
                strict_filtering = not strict_filtering
                status = "AKTIF" if strict_filtering else "NONAKTIF"
                print(f"Filter false positive ketat: {status}")
                print("Tekan 'f' lagi untuk toggle, 'm' untuk mode sensitif")
            elif key == ord('d'):
                # Toggle debug mode - print semua deteksi ke console
                if debug_info:
                    print(f"\n=== DEBUG FRAME {frame_count} ===")
                    for info in debug_info:
                        if info['detected']:
                            status = "DETECTED"
                        elif info.get('false_positive', False):
                            status = "FALSE_POS"
                        else:
                            status = "MISSED"
                        print(f"{status}: {info['type']} | Conf: {info['conf']:.2f} | Area: {info['area']:.1f}% | Ratio: {info['ratio']:.1f} | Pos: ({info['pos'][0]:.0f},{info['pos'][1]:.0f}) | Size_OK: {info['size_ok']} | ROI_OK: {info['roi_ok']} | FP: {info.get('false_positive', False)}")
                    print("=" * 50)

        except Exception as e:
            print(f"Error displaying frame: {e}")
            # Tetap cek keyboard input meskipun display error
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                print("Program dihentikan oleh user (tekan 'q')")
                break

        # Print informasi setiap 100 frame
        if frame_count % 100 == 0:
            print(f"Frame {frame_count}: {current_frame_vehicles} kendaraan terdeteksi")
            print(f"Total per frame: {vehicle_count} | Total UNIK: {tracking_stats['total_unique']}")
            if tracking_stats['by_type']:
                print("Kendaraan unik per jenis:", tracking_stats['by_type'])

except KeyboardInterrupt:
    print("\nProgram dihentikan oleh user (Ctrl+C)")
except Exception as e:
    print(f"\nError tidak terduga: {e}")
    print("Program akan tetap berjalan. Tekan 'q' untuk keluar dengan aman.")

# Bersihkan sumber daya
try:
    final_stats = vehicle_tracker.get_statistics()
    print(f"\n=== STATISTIK AKHIR ===")
    print(f"Total deteksi per frame: {vehicle_count}")
    print(f"Total kendaraan UNIK: {final_stats['total_unique']}")
    if final_stats['by_type']:
        print("Breakdown per jenis kendaraan:")
        for vehicle_type, count in final_stats['by_type'].items():
            print(f"  - {vehicle_type}: {count} kendaraan unik")
    print("=" * 40)
except:
    print("Error saat menampilkan statistik akhir")

try:
    cap.release()
    cv2.destroyAllWindows()
    print("Program selesai dengan aman.")
except:
    print("Error saat membersihkan resources")
