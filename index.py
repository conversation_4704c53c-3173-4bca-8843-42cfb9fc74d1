import cv2
import torch
import numpy as np

# Ganti 'http://source-url.com/stream' dengan URL sumber video
stream_url = 'http://stream.cctv.malangkota.go.id/WebRTCApp/streams/697348239578014654770613.m3u8'

# Memuat model YOLOv5 dengan force_reload untuk mengatasi cache yang rusak
try:
    print("Loading YOLOv5 model...")
    model = torch.hub.load('ultralytics/yolov5', 'yolov5m')  # Model medium YOLOv5 (yolov5m)
    print("YOLOv5m model loaded successfully!")
except Exception as e:
    print(f"Error loading yolov5m: {e}")
    print("Trying yolov5s as fallback...")
    try:
        model = torch.hub.load('ultralytics/yolov5', 'yolov5s')  # Fallback ke model kecil
        print("YOLOv5s model loaded successfully!")
    except Exception as e2:
        print(f"Error loading yolov5s: {e2}")
        print("Trying local model files...")
        # Coba gunakan file lokal jika ada
        if torch.cuda.is_available():
            device = 'cuda'
        else:
            device = 'cpu'

        # Coba load model lokal
        try:
            model = torch.hub.load('ultralytics/yolov5', 'custom', path='yolov5s.pt')
            print("Local YOLOv5s model loaded successfully!")
        except:
            print("Loading basic YOLOv5s model...")
            model = torch.hub.load('ultralytics/yolov5', 'yolov5s', trust_repo=True)

# Konfigurasi model untuk deteksi yang seimbang
model.conf = 0.15  # Confidence threshold sangat rendah untuk menangkap motor yang sulit terdeteksi
model.iou = 0.2    # IoU threshold lebih rendah untuk mendeteksi objek yang berdekatan
model.max_det = 1000  # Maksimum deteksi per gambar

# Class IDs untuk semua jenis kendaraan di COCO dataset
VEHICLE_CLASSES = [
    2,   # car
    3,   # motorcycle
    5,   # bus
    7,   # truck
    1,   # bicycle (sepeda juga kendaraan)
]

# Fungsi untuk validasi ukuran objek berdasarkan jenis kendaraan
def validate_vehicle_size(x1, y1, x2, y2, class_id, frame_width, frame_height):
    """Validasi ukuran objek untuk mengurangi false positive"""
    width = x2 - x1
    height = y2 - y1
    area = width * height

    # Persentase area relatif terhadap frame
    relative_area = area / (frame_width * frame_height)

    # Rasio aspek (width/height)
    aspect_ratio = width / height if height > 0 else 0

    # Validasi berdasarkan jenis kendaraan (parameter lebih fleksibel)
    if class_id == 2:  # car
        # Mobil: area 0.0005-0.4, aspect ratio 0.8-4.0 (lebih fleksibel)
        return 0.0005 <= relative_area <= 0.4 and 0.8 <= aspect_ratio <= 4.0
    elif class_id == 3:  # motorcycle
        # Motor: area 0.00005-0.3, aspect ratio 0.2-5.0 (sangat fleksibel untuk motor berjajar dan kecil)
        return 0.00005 <= relative_area <= 0.3 and 0.2 <= aspect_ratio <= 5.0
    elif class_id == 5:  # bus
        # Bus: area 0.005-0.6, aspect ratio 1.5-5.0 (lebih fleksibel)
        return 0.005 <= relative_area <= 0.6 and 1.5 <= aspect_ratio <= 5.0
    elif class_id == 7:  # truck
        # Truk: area 0.003-0.5, aspect ratio 1.0-4.0 (lebih fleksibel)
        return 0.003 <= relative_area <= 0.5 and 1.0 <= aspect_ratio <= 4.0
    elif class_id == 1:  # bicycle
        # Sepeda: area 0.0001-0.08, aspect ratio 0.5-2.5 (lebih fleksibel)
        return 0.0001 <= relative_area <= 0.08 and 0.5 <= aspect_ratio <= 2.5

    return True  # Default untuk kelas lain

# Fungsi untuk validasi tambahan khusus motor dengan confidence rendah
def validate_low_confidence_motorcycle(x1, y1, x2, y2, conf, frame_width, frame_height):
    """Validasi ketat untuk motor dengan confidence rendah untuk mengurangi false positive"""
    width = x2 - x1
    height = y2 - y1
    area = width * height
    relative_area = area / (frame_width * frame_height)
    aspect_ratio = width / height if height > 0 else 0

    # Untuk motor dengan confidence sangat rendah, validasi lebih ketat
    if conf < 0.15:
        # Harus memenuhi kriteria ukuran yang lebih ketat
        size_valid = (0.0001 <= relative_area <= 0.1 and
                     0.4 <= aspect_ratio <= 3.5)

        # Harus memiliki ukuran minimum yang masuk akal untuk motor
        min_size_valid = width >= 15 and height >= 15

        # Tidak boleh terlalu besar (kemungkinan false positive)
        max_size_valid = relative_area <= 0.05

        return size_valid and min_size_valid and max_size_valid

    # Untuk confidence sedang, validasi normal
    return True

# Fungsi untuk mendeteksi dan menolak false positive umum
def is_likely_false_positive(x1, y1, x2, y2, conf, class_id, frame_width, frame_height):
    """Deteksi objek yang kemungkinan false positive"""
    width = x2 - x1
    height = y2 - y1
    area = width * height
    relative_area = area / (frame_width * frame_height)
    aspect_ratio = width / height if height > 0 else 0
    center_y = (y1 + y2) / 2

    # Khusus untuk motor dengan confidence rendah
    if class_id == 3 and conf < 0.2:
        false_positive_score = 0

        # 1. Objek terlalu kecil untuk menjadi motor
        if relative_area < 0.00005:  # Sangat kecil
            false_positive_score += 2

        # 2. Aspect ratio yang sangat tidak wajar untuk motor
        if aspect_ratio < 0.15 or aspect_ratio > 6.0:
            false_positive_score += 2

        # 3. Objek di area yang sangat tidak mungkin (pojok atas frame)
        if center_y < frame_height * 0.05:  # 5% teratas
            false_positive_score += 1

        # 4. Objek terlalu tipis (kemungkinan garis atau bayangan)
        if width < 10 or height < 10:
            false_positive_score += 2

        # 5. Objek dengan ukuran yang sangat tidak proporsional
        if width > height * 8 or height > width * 8:  # Sangat tidak proporsional
            false_positive_score += 1

        # 6. Area terlalu besar untuk confidence yang rendah
        if relative_area > 0.02 and conf < 0.1:  # Besar tapi confidence rendah
            false_positive_score += 1

        # Jika skor false positive tinggi, kemungkinan bukan motor
        return false_positive_score >= 3

    return False

# Fungsi untuk validasi posisi objek (ROI - Region of Interest)
def is_in_road_area(x1, y1, x2, y2, frame_width, frame_height):
    """Validasi apakah objek berada di area jalan (bukan trotoar)"""
    center_x = (x1 + x2) / 2
    center_y = (y1 + y2) / 2

    # Definisi area jalan (sangat luas untuk motor di pinggir)
    # Area yang sangat fleksibel untuk motor yang parkir/berhenti di pinggir
    road_left = frame_width * 0.05   # 5% dari kiri (sangat sempit untuk motor pinggir)
    road_right = frame_width * 0.95  # 5% dari kanan (sangat sempit untuk motor pinggir)
    road_top = frame_height * 0.1    # 10% dari atas (lebih rendah lagi)

    # Objek harus berada di area jalan
    return road_left <= center_x <= road_right and center_y >= road_top

# Fungsi untuk mendeteksi motor berjajar
def detect_clustered_motorcycles(detections):
    """Deteksi tambahan untuk motor yang berjajar/berkelompok"""
    motorcycle_detections = []

    # Filter hanya deteksi motor dengan confidence rendah-sedang
    for detection in detections:
        _, _, _, _, conf, class_id = detection
        class_id = int(class_id)

        if class_id == 3 and 0.08 <= conf <= 0.5:  # Motor dengan confidence rendah-sedang (diperluas)
            motorcycle_detections.append(detection)

    additional_detections = []

    # Cari area dengan banyak deteksi motor untuk analisis lebih lanjut
    for i, det1 in enumerate(motorcycle_detections):
        x1_1, y1_1, x2_1, y2_1, conf1, _ = det1
        center1_x = (x1_1 + x2_1) / 2
        center1_y = (y1_1 + y2_1) / 2

        # Cari deteksi motor lain yang berdekatan
        nearby_motors = 0
        for j, det2 in enumerate(motorcycle_detections):
            if i == j:
                continue

            x1_2, y1_2, x2_2, y2_2, _, _ = det2
            center2_x = (x1_2 + x2_2) / 2
            center2_y = (y1_2 + y2_2) / 2

            # Hitung jarak antar center
            distance = ((center1_x - center2_x)**2 + (center1_y - center2_y)**2)**0.5

            # Jika jarak dekat (dalam radius tertentu)
            if distance < 100:  # 100 pixel radius
                nearby_motors += 1

        # Jika ada motor lain di sekitar, naikkan confidence
        if nearby_motors >= 1:
            # Buat deteksi tambahan dengan confidence yang disesuaikan
            adjusted_conf = min(0.5, conf1 + 0.15)  # Naikkan confidence lebih besar
            additional_detections.append([x1_1, y1_1, x2_1, y2_1, adjusted_conf, 3])

    return additional_detections

# Fungsi untuk mendeteksi motor yang tertutup sebagian (occluded)
def detect_occluded_motorcycles(detections):
    """Deteksi motor yang tertutup sebagian oleh kendaraan lain atau objek"""
    occluded_detections = []

    # Cari deteksi dengan confidence sangat rendah yang mungkin motor tertutup
    low_conf_detections = []
    vehicle_detections = []

    for detection in detections:
        _, _, _, _, conf, class_id = detection
        class_id = int(class_id)

        if class_id == 3 and 0.05 <= conf <= 0.35:  # Motor confidence sangat rendah (diperluas lebih jauh)
            low_conf_detections.append(detection)
        elif class_id in [2, 5, 7] and conf > 0.2:  # Kendaraan besar yang mungkin menutupi (threshold lebih rendah)
            vehicle_detections.append(detection)

    # Analisis setiap deteksi motor confidence rendah
    for motor_det in low_conf_detections:
        x1_m, y1_m, x2_m, y2_m, conf_m, _ = motor_det
        motor_center_x = (x1_m + x2_m) / 2
        motor_center_y = (y1_m + y2_m) / 2
        motor_area = (x2_m - x1_m) * (y2_m - y1_m)

        # Cek apakah ada kendaraan besar di sekitar yang mungkin menutupi
        is_likely_occluded = False
        occlusion_score = 0

        for vehicle_det in vehicle_detections:
            x1_v, y1_v, x2_v, y2_v, conf_v, class_v = vehicle_det
            vehicle_center_x = (x1_v + x2_v) / 2
            vehicle_center_y = (y1_v + y2_v) / 2

            # Hitung jarak antara motor dan kendaraan
            distance = ((motor_center_x - vehicle_center_x)**2 + (motor_center_y - vehicle_center_y)**2)**0.5

            # Cek apakah motor berada di dekat atau di belakang kendaraan besar
            if distance < 200:  # Dalam radius 200 pixel (diperluas)
                # Cek posisi relatif (motor di belakang/samping kendaraan)
                if abs(motor_center_y - vehicle_center_y) < 50:  # Sejajar secara vertikal
                    occlusion_score += 1

                # Cek apakah motor memiliki aspect ratio yang aneh (terpotong)
                motor_width = x2_m - x1_m
                motor_height = y2_m - y1_m
                aspect_ratio = motor_width / motor_height if motor_height > 0 else 0

                # Motor tertutup biasanya memiliki aspect ratio yang tidak normal
                if aspect_ratio < 0.3 or aspect_ratio > 3.5:
                    occlusion_score += 1

                # Cek ukuran motor yang sangat kecil (kemungkinan tertutup)
                relative_area = motor_area / (1280 * 720)
                if 0.0001 <= relative_area <= 0.001:  # Sangat kecil
                    occlusion_score += 1

        # Jika skor okklusi tinggi, anggap sebagai motor tertutup
        if occlusion_score >= 2:
            is_likely_occluded = True

        # Tambahan: Cek pola deteksi yang tidak lengkap
        # Motor tertutup sering terdeteksi sebagai bagian kecil
        motor_width = x2_m - x1_m
        motor_height = y2_m - y1_m

        # Jika sangat tipis atau sangat pendek, kemungkinan tertutup
        if motor_width < 20 or motor_height < 20:
            is_likely_occluded = True

        # Jika teridentifikasi sebagai motor tertutup, naikkan confidence
        if is_likely_occluded:
            # Naikkan confidence berdasarkan skor okklusi
            confidence_boost = min(0.2, occlusion_score * 0.08)
            adjusted_conf = min(0.45, conf_m + confidence_boost)

            # Perbaiki bounding box jika terlalu kecil
            if motor_width < 30:
                # Perluas width
                center_x = (x1_m + x2_m) / 2
                x1_m = center_x - 15
                x2_m = center_x + 15

            if motor_height < 30:
                # Perluas height
                center_y = (y1_m + y2_m) / 2
                y1_m = center_y - 15
                y2_m = center_y + 15

            occluded_detections.append([x1_m, y1_m, x2_m, y2_m, adjusted_conf, 3])

    return occluded_detections

# Fungsi untuk mendeteksi motor yang berdampingan (side by side)
def detect_side_by_side_motorcycles(detections, frame_width, frame_height):
    """Deteksi khusus untuk motor yang berdampingan atau berjajar horizontal"""
    side_by_side_detections = []

    # Cari semua deteksi motor dengan confidence sangat rendah
    low_conf_motors = []
    for detection in detections:
        _, _, _, _, conf, class_id = detection
        class_id = int(class_id)

        if class_id == 3 and 0.02 <= conf <= 0.3:  # Motor confidence sangat rendah
            low_conf_motors.append(detection)

    # Analisis setiap motor confidence rendah untuk mencari pola side-by-side
    for motor_det in low_conf_motors:
        x1_m, y1_m, x2_m, y2_m, conf_m, _ = motor_det
        motor_center_x = (x1_m + x2_m) / 2
        motor_center_y = (y1_m + y2_m) / 2
        motor_width = x2_m - x1_m
        motor_height = y2_m - y1_m

        # Cari motor lain yang berdekatan secara horizontal
        side_by_side_score = 0
        nearby_motors = 0

        for other_det in detections:
            x1_o, y1_o, x2_o, y2_o, _, class_o = other_det
            class_o = int(class_o)

            # Skip jika sama atau bukan motor
            if (x1_o == x1_m and y1_o == y1_m) or class_o != 3:
                continue

            other_center_x = (x1_o + x2_o) / 2
            other_center_y = (y1_o + y2_o) / 2

            # Hitung jarak horizontal dan vertikal
            horizontal_distance = abs(motor_center_x - other_center_x)
            vertical_distance = abs(motor_center_y - other_center_y)

            # Cek apakah motor berada di samping (horizontal distance kecil, vertical distance besar)
            if horizontal_distance < 80 and vertical_distance < 40:  # Sangat dekat secara vertikal
                side_by_side_score += 2
                nearby_motors += 1
            elif horizontal_distance < 150 and vertical_distance < 60:  # Agak dekat
                side_by_side_score += 1
                nearby_motors += 1

        # Cek karakteristik motor yang mungkin terhalang oleh motor lain
        aspect_ratio = motor_width / motor_height if motor_height > 0 else 0
        relative_area = (motor_width * motor_height) / (frame_width * frame_height)

        # Motor yang berdampingan sering memiliki karakteristik tertentu
        if aspect_ratio < 0.4 or aspect_ratio > 3.5:  # Aspect ratio tidak normal
            side_by_side_score += 1

        if relative_area < 0.002:  # Sangat kecil
            side_by_side_score += 1

        # Jika ada indikasi motor berdampingan
        if side_by_side_score >= 2 and nearby_motors >= 1:
            # Naikkan confidence berdasarkan skor
            confidence_boost = min(0.3, side_by_side_score * 0.08)
            adjusted_conf = min(0.5, conf_m + confidence_boost)

            # Perbaiki bounding box jika terlalu kecil
            if motor_width < 30:
                center_x = (x1_m + x2_m) / 2
                x1_m = max(0, center_x - 20)
                x2_m = min(frame_width, center_x + 20)

            if motor_height < 30:
                center_y = (y1_m + y2_m) / 2
                y1_m = max(0, center_y - 20)
                y2_m = min(frame_height, center_y + 20)

            side_by_side_detections.append([x1_m, y1_m, x2_m, y2_m, adjusted_conf, 3])

    return side_by_side_detections

# Fungsi untuk mendeteksi motor di pinggir/area marginal
def detect_edge_motorcycles(detections, frame_width, frame_height):
    """Deteksi motor yang berada di pinggir frame atau area marginal"""
    edge_detections = []

    # Cari deteksi motor dengan confidence sangat rendah di area pinggir
    for detection in detections:
        x1, y1, x2, y2, conf, class_id = detection
        class_id = int(class_id)

        if class_id == 3 and 0.03 <= conf <= 0.25:  # Motor confidence sangat rendah (diperluas)
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2
            width = x2 - x1
            height = y2 - y1
            area = width * height
            relative_area = area / (frame_width * frame_height)

            # Cek apakah motor berada di area pinggir
            is_edge_position = False
            edge_score = 0

            # Area pinggir kiri (0-15% dari lebar frame)
            if center_x < frame_width * 0.15:
                is_edge_position = True
                edge_score += 1

            # Area pinggir kanan (85-100% dari lebar frame)
            elif center_x > frame_width * 0.85:
                is_edge_position = True
                edge_score += 1

            # Area pinggir atas (0-20% dari tinggi frame)
            if center_y < frame_height * 0.2:
                edge_score += 1

            # Area pinggir bawah (80-100% dari tinggi frame)
            elif center_y > frame_height * 0.8:
                edge_score += 1

            # Cek karakteristik motor yang mungkin terpotong atau jauh
            if relative_area < 0.005:  # Sangat kecil
                edge_score += 1

            # Aspect ratio yang menunjukkan motor terpotong
            aspect_ratio = width / height if height > 0 else 0
            if aspect_ratio < 0.5 or aspect_ratio > 3.0:
                edge_score += 1

            # Jika motor di pinggir dengan karakteristik yang mendukung
            if is_edge_position and edge_score >= 2:
                # Naikkan confidence berdasarkan edge score
                confidence_boost = min(0.25, edge_score * 0.05)
                adjusted_conf = min(0.4, conf + confidence_boost)

                # Perbaiki bounding box jika terlalu kecil
                if width < 25:
                    x1 = max(0, center_x - 12.5)
                    x2 = min(frame_width, center_x + 12.5)

                if height < 25:
                    y1 = max(0, center_y - 12.5)
                    y2 = min(frame_height, center_y + 12.5)

                edge_detections.append([x1, y1, x2, y2, adjusted_conf, 3])

    return edge_detections

# Membuka stream
cap = cv2.VideoCapture(stream_url)
cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Buffer lebih kecil untuk real-time
cap.set(cv2.CAP_PROP_FPS, 30)  # Set FPS

if not cap.isOpened():
    print("Gagal membuka stream!")
    exit()

# Mengurangi frame skipping untuk deteksi yang lebih baik
frame_skip = 1  # Proses setiap frame (atau ubah ke 2 jika performa lambat)
frame_count = 0
vehicle_count = 0  # Counter untuk kendaraan yang terdeteksi
strict_filtering = True  # Toggle untuk filtering false positive yang ketat
while True:
    ret, frame = cap.read()

    if not ret:
        print("Gagal membaca frame!")
        break

    # Simpan frame original untuk display
    original_frame = frame.copy()

    # Resize dengan resolusi yang lebih tinggi untuk deteksi yang lebih baik
    frame = cv2.resize(frame, (1280, 720))  # Resolusi lebih tinggi

    frame_count += 1
    if frame_count % frame_skip != 0:
        continue  # Lewati frame

    # Preprocessing untuk meningkatkan deteksi motor yang sulit terlihat
    # Peningkatan kontras dan brightness untuk motor yang gelap/tertutup
    enhanced_frame = cv2.convertScaleAbs(frame, alpha=1.15, beta=15)  # Peningkatan kontras dan brightness

    # Gabungkan frame original dan enhanced untuk deteksi yang lebih baik
    # Gunakan enhanced frame untuk deteksi
    detection_frame = enhanced_frame

    # Deteksi objek menggunakan YOLOv5 dengan frame yang telah dienhance
    results = model(detection_frame)  # Hasil deteksi dari enhanced frame

    # Ambil hasil deteksi
    detections = results.xyxy[0].cpu().numpy()  # Bounding boxes (x1, y1, x2, y2, conf, class)

    # Tambahkan deteksi motor berjajar
    additional_motorcycles = detect_clustered_motorcycles(detections)
    if additional_motorcycles:
        # Gabungkan deteksi tambahan dengan deteksi asli
        additional_array = np.array(additional_motorcycles)
        detections = np.vstack([detections, additional_array])

    # Tambahkan deteksi motor tertutup
    occluded_motorcycles = detect_occluded_motorcycles(detections)
    if occluded_motorcycles:
        # Gabungkan deteksi motor tertutup dengan deteksi yang sudah ada
        occluded_array = np.array(occluded_motorcycles)
        detections = np.vstack([detections, occluded_array])

    # Tambahkan deteksi motor berdampingan
    side_by_side_motorcycles = detect_side_by_side_motorcycles(detections, 1280, 720)
    if side_by_side_motorcycles:
        # Gabungkan deteksi motor berdampingan dengan deteksi yang sudah ada
        side_by_side_array = np.array(side_by_side_motorcycles)
        detections = np.vstack([detections, side_by_side_array])

    # Tambahkan deteksi motor di pinggir
    edge_motorcycles = detect_edge_motorcycles(detections, 1280, 720)
    if edge_motorcycles:
        # Gabungkan deteksi motor pinggir dengan deteksi yang sudah ada
        edge_array = np.array(edge_motorcycles)
        detections = np.vstack([detections, edge_array])

    # Reset frame untuk display (gunakan original frame yang di-resize)
    display_frame = cv2.resize(original_frame, (1280, 720))

    # Counter untuk frame ini
    current_frame_vehicles = 0
    debug_info = []  # Untuk menyimpan info debugging

    # Loop untuk setiap deteksi
    for detection in detections:
        x1, y1, x2, y2, conf, class_id = detection
        class_id = int(class_id)

        # Filter deteksi kendaraan dengan validasi yang lebih fleksibel
        # Cek confidence threshold yang disesuaikan (sangat rendah untuk motor)
        if class_id == 3:  # Motor
            confidence_ok = conf > 0.05   # Threshold sangat rendah untuk motor (diperluas)
        else:
            confidence_ok = conf > 0.3   # Threshold normal untuk kendaraan lain

        # Cek apakah kelas kendaraan
        is_vehicle = class_id in VEHICLE_CLASSES

        # Validasi ukuran (opsional untuk kendaraan kecil/jauh)
        size_ok = validate_vehicle_size(x1, y1, x2, y2, class_id, 1280, 720)

        # Validasi ROI (opsional untuk kendaraan di pinggir)
        roi_ok = is_in_road_area(x1, y1, x2, y2, 1280, 720)

        # Cek apakah kemungkinan false positive (hanya jika strict filtering aktif)
        is_false_positive = strict_filtering and is_likely_false_positive(x1, y1, x2, y2, conf, class_id, 1280, 720)

        # Logika deteksi yang lebih fleksibel, khusus untuk motor:
        # - Confidence tinggi (>0.5): langsung terima tanpa validasi lain
        # - Confidence sedang (0.3-0.5): perlu validasi ukuran ATAU ROI
        # - Confidence rendah (<0.3): tolak, kecuali motor dengan validasi khusus
        if confidence_ok and is_vehicle and not is_false_positive:  # Tambahkan filter false positive
            if conf > 0.5:  # Confidence tinggi, langsung terima
                should_detect = True
            elif conf > 0.4:  # Confidence sedang-tinggi, perlu salah satu validasi
                should_detect = size_ok or roi_ok
            elif class_id == 3:  # Motor dengan confidence sedang-rendah, validasi khusus
                if conf < 0.15:  # Motor confidence sangat rendah, validasi ketat
                    low_conf_valid = validate_low_confidence_motorcycle(x1, y1, x2, y2, conf, 1280, 720)
                    should_detect = low_conf_valid and (size_ok or roi_ok)
                else:  # Motor confidence sedang-rendah, lebih toleran
                    should_detect = size_ok or roi_ok
            else:  # Kendaraan lain dengan confidence sedang-rendah, perlu kedua validasi
                should_detect = size_ok and roi_ok

        else:
            should_detect = False

        # Debug info untuk semua deteksi kendaraan (terdeteksi atau tidak)
        if is_vehicle:
            width = x2 - x1
            height = y2 - y1
            area_percent = (width * height) / (1280 * 720) * 100
            aspect_ratio = width / height if height > 0 else 0
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2

            vehicle_names = {2: "Mobil", 3: "Motor", 5: "Bus", 7: "Truk", 1: "Sepeda"}
            vehicle_name = vehicle_names.get(class_id, "Kendaraan")

            debug_info.append({
                'type': vehicle_name,
                'conf': conf,
                'area': area_percent,
                'ratio': aspect_ratio,
                'pos': (center_x, center_y),
                'size_ok': size_ok,
                'roi_ok': roi_ok,
                'false_positive': is_false_positive,
                'detected': should_detect
            })

        if should_detect:
            current_frame_vehicles += 1

            # Tentukan warna berdasarkan jenis kendaraan
            if class_id == 2:  # car
                color = (0, 255, 0)  # Hijau
                vehicle_type = "Mobil"
            elif class_id == 3:  # motorcycle
                # Warna berbeda untuk motor berdasarkan confidence dan kondisi
                if conf > 0.4:
                    color = (0, 0, 255)  # Merah untuk motor confidence tinggi
                    vehicle_type = "Motor"
                elif conf > 0.3:
                    color = (0, 100, 255)  # Orange untuk motor berjajar
                    vehicle_type = "Motor*"
                elif conf > 0.2:
                    color = (255, 0, 255)  # Magenta untuk motor berdampingan
                    vehicle_type = "Motor="  # Tanda = untuk motor berdampingan
                elif conf > 0.1:
                    color = (0, 255, 255)  # Kuning untuk motor tertutup
                    vehicle_type = "Motor#"  # Tanda # untuk motor tertutup
                else:
                    color = (255, 255, 0)  # Cyan untuk motor di pinggir
                    vehicle_type = "Motor~"  # Tanda ~ untuk motor pinggir
            elif class_id == 5:  # bus
                color = (255, 0, 0)  # Biru
                vehicle_type = "Bus"
            elif class_id == 7:  # truck
                color = (0, 255, 255)  # Kuning
                vehicle_type = "Truk"
            elif class_id == 1:  # bicycle
                color = (255, 0, 255)  # Magenta
                vehicle_type = "Sepeda"
            else:
                color = (128, 128, 128)  # Abu-abu
                vehicle_type = "Kendaraan"

            # Gambar bounding box dengan ketebalan yang lebih tebal
            cv2.rectangle(display_frame, (int(x1), int(y1)), (int(x2), int(y2)), color, 3)

            # Label dengan informasi lebih detail termasuk ukuran untuk debugging
            width = x2 - x1
            height = y2 - y1
            area_percent = (width * height) / (1280 * 720) * 100
            label = f"{vehicle_type}: {conf:.2f} ({area_percent:.1f}%)"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]

            # Background untuk label
            cv2.rectangle(display_frame, (int(x1), int(y1)-label_size[1]-10),
                         (int(x1)+label_size[0], int(y1)), color, -1)

            # Text label
            cv2.putText(display_frame, label, (int(x1), int(y1)-5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

    # Update total vehicle count
    if current_frame_vehicles > 0:
        vehicle_count += current_frame_vehicles

    # Gambar ROI (Region of Interest) untuk debugging
    road_left = int(1280 * 0.05)
    road_right = int(1280 * 0.95)
    road_top = int(720 * 0.1)
    cv2.line(display_frame, (road_left, 0), (road_left, 720), (255, 255, 0), 2)  # Garis kiri
    cv2.line(display_frame, (road_right, 0), (road_right, 720), (255, 255, 0), 2)  # Garis kanan
    cv2.line(display_frame, (0, road_top), (1280, road_top), (255, 255, 0), 2)  # Garis atas

    # Tambahkan informasi statistik di frame
    info_text = f"Kendaraan terdeteksi: {current_frame_vehicles} | Total: {vehicle_count}"
    cv2.putText(display_frame, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    cv2.putText(display_frame, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 1)

    # Tambahkan informasi frame dan confidence threshold
    filter_status = "ON" if strict_filtering else "OFF"
    frame_info = f"Frame: {frame_count} | Conf: {model.conf:.2f} | FP Filter: {filter_status} | 'm'=sensitif 'f'=filter"
    cv2.putText(display_frame, frame_info, (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
    cv2.putText(display_frame, frame_info, (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)

    # Tambahkan informasi ROI dan jenis motor
    roi_info = "ROI: Cyan=zona | Motor*=berjajar | Motor==berdampingan | Motor#=tertutup | Motor~=pinggir"
    cv2.putText(display_frame, roi_info, (10, 110), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 0), 2)

    # Hitung jumlah motor berjajar, berdampingan, tertutup, dan pinggir yang terdeteksi
    clustered_motors = len([info for info in debug_info if info['type'] == 'Motor*' and info['detected']])
    side_by_side_motors = len([info for info in debug_info if info['type'] == 'Motor=' and info['detected']])
    occluded_motors = len([info for info in debug_info if info['type'] == 'Motor#' and info['detected']])
    edge_motors = len([info for info in debug_info if info['type'] == 'Motor~' and info['detected']])

    info_y = 140
    if clustered_motors > 0:
        cluster_info = f"Motor berjajar: {clustered_motors}"
        cv2.putText(display_frame, cluster_info, (10, info_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 100, 255), 2)
        info_y += 22

    if side_by_side_motors > 0:
        side_by_side_info = f"Motor berdampingan: {side_by_side_motors}"
        cv2.putText(display_frame, side_by_side_info, (10, info_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 255), 2)
        info_y += 22

    if occluded_motors > 0:
        occluded_info = f"Motor tertutup: {occluded_motors}"
        cv2.putText(display_frame, occluded_info, (10, info_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 2)
        info_y += 22

    if edge_motors > 0:
        edge_info = f"Motor pinggir: {edge_motors}"
        cv2.putText(display_frame, edge_info, (10, info_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 2)

    # Tambahkan debug info untuk kendaraan yang tidak terdeteksi
    debug_y = 150
    for info in debug_info:
        if not info['detected']:  # Hanya tampilkan yang tidak terdeteksi
            if info.get('false_positive', False):
                debug_text = f"FALSE_POS: {info['type']} conf:{info['conf']:.2f} area:{info['area']:.1f}% ratio:{info['ratio']:.1f}"
                cv2.putText(display_frame, debug_text, (10, debug_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
            else:
                debug_text = f"MISSED: {info['type']} conf:{info['conf']:.2f} area:{info['area']:.1f}% ratio:{info['ratio']:.1f}"
                cv2.putText(display_frame, debug_text, (10, debug_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
            debug_y += 20
            if debug_y > 300:  # Batasi jumlah debug info
                break

    # Tampilkan frame dengan deteksi kendaraan
    cv2.imshow('Live Streaming - Deteksi Kendaraan (Enhanced)', display_frame)

    # Tekan 'q' untuk keluar, 'r' untuk reset counter, '+'/'-' untuk adjust confidence
    key = cv2.waitKey(1) & 0xFF
    if key == ord('q'):
        break
    elif key == ord('r'):
        vehicle_count = 0
        print("Counter kendaraan direset!")
    elif key == ord('+') or key == ord('='):
        model.conf = min(0.9, model.conf + 0.05)
        print(f"Confidence threshold dinaikkan ke: {model.conf:.2f}")
    elif key == ord('-'):
        model.conf = max(0.05, model.conf - 0.05)  # Minimum threshold lebih rendah
        print(f"Confidence threshold diturunkan ke: {model.conf:.2f}")
    elif key == ord('m'):
        # Toggle mode deteksi motor - turunkan threshold khusus untuk motor
        if model.conf > 0.1:
            model.conf = 0.05
            print("Mode deteksi motor ULTRA SENSITIF aktif! (conf=0.05)")
        else:
            model.conf = 0.25
            print("Mode deteksi normal aktif! (conf=0.25)")
    elif key == ord('f'):
        # Toggle strict false positive filtering
        strict_filtering = not strict_filtering
        status = "AKTIF" if strict_filtering else "NONAKTIF"
        print(f"Filter false positive ketat: {status}")
        print("Tekan 'f' lagi untuk toggle, 'm' untuk mode sensitif")
    elif key == ord('d'):
        # Toggle debug mode - print semua deteksi ke console
        if debug_info:
            print(f"\n=== DEBUG FRAME {frame_count} ===")
            for info in debug_info:
                if info['detected']:
                    status = "DETECTED"
                elif info.get('false_positive', False):
                    status = "FALSE_POS"
                else:
                    status = "MISSED"
                print(f"{status}: {info['type']} | Conf: {info['conf']:.2f} | Area: {info['area']:.1f}% | Ratio: {info['ratio']:.1f} | Pos: ({info['pos'][0]:.0f},{info['pos'][1]:.0f}) | Size_OK: {info['size_ok']} | ROI_OK: {info['roi_ok']} | FP: {info.get('false_positive', False)}")
            print("=" * 50)

    # Print informasi setiap 100 frame
    if frame_count % 100 == 0:
        print(f"Frame {frame_count}: {current_frame_vehicles} kendaraan terdeteksi, Total: {vehicle_count}")

# Bersihkan sumber daya
print(f"\nProgram selesai. Total kendaraan yang terdeteksi: {vehicle_count}")
cap.release()
cv2.destroyAllWindows()
